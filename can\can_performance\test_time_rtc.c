#include <time.h>
#include <stdio.h>
#include <unistd.h>

// 测试程序：检查时间函数是否会访问 RTC
int main() {
    struct timespec time = {0, 0};
    
    printf("测试 clock_gettime(CLOCK_MONOTONIC) 是否访问 RTC...\n");
    
    // 这是程序中使用的时间函数
    clock_gettime(CLOCK_MONOTONIC, &time);
    
    printf("时间获取成功: %ld.%09ld\n", time.tv_sec, time.tv_nsec);
    
    // 测试其他时间函数
    printf("测试其他时间函数...\n");
    
    time_t t = time(NULL);
    printf("time() 返回: %ld\n", t);
    
    return 0;
}
