#!/bin/bash

# RTC设备占用调试脚本
# 用于分析为什么CAN测试程序会占用RTC设备

echo "=== RTC设备占用调试脚本 ==="
echo "时间: $(date)"
echo ""

# 1. 检查RTC设备状态
echo "1. 检查RTC设备:"
ls -la /dev/rtc* 2>/dev/null || echo "未找到RTC设备"
echo ""

# 2. 检查当前占用RTC的进程
echo "2. 当前占用RTC设备的进程:"
lsof /dev/rtc* 2>/dev/null || echo "没有进程占用RTC设备"
echo ""

# 3. 检查CAN相关进程
echo "3. CAN相关进程:"
ps aux | grep can_type | grep -v grep || echo "没有找到can_type进程"
echo ""

# 4. 如果有CAN进程，检查其打开的文件描述符
CAN_PIDS=$(ps aux | grep can_type | grep -v grep | awk '{print $2}')
if [ ! -z "$CAN_PIDS" ]; then
    echo "4. CAN进程打开的文件描述符:"
    for pid in $CAN_PIDS; do
        echo "进程 $pid 打开的文件:"
        ls -la /proc/$pid/fd/ 2>/dev/null | grep -E "(rtc|time)" || echo "  未发现RTC相关文件描述符"
        echo ""
        
        echo "进程 $pid 的内存映射:"
        cat /proc/$pid/maps 2>/dev/null | grep -E "(rtc|time)" || echo "  未发现RTC相关内存映射"
        echo ""
    done
fi

# 5. 检查系统调用跟踪（如果strace可用）
if command -v strace >/dev/null 2>&1; then
    echo "5. 系统调用跟踪可用，可以使用以下命令跟踪CAN进程:"
    echo "   strace -e trace=openat,open,close -f -p <CAN_PID> 2>&1 | grep rtc"
    echo ""
fi

# 6. 检查系统时间相关服务
echo "6. 系统时间相关服务:"
ps aux | grep -E "(ntp|chrony|systemd-time)" | grep -v grep || echo "未发现时间同步服务"
echo ""

# 7. 检查内核模块
echo "7. RTC相关内核模块:"
lsmod | grep rtc || echo "未发现RTC内核模块"
echo ""

# 8. 检查设备树或硬件信息
echo "8. RTC硬件信息:"
if [ -d /sys/class/rtc ]; then
    for rtc in /sys/class/rtc/rtc*; do
        if [ -d "$rtc" ]; then
            echo "RTC设备: $(basename $rtc)"
            echo "  名称: $(cat $rtc/name 2>/dev/null || echo '未知')"
            echo "  时间: $(cat $rtc/time 2>/dev/null || echo '无法读取')"
            echo "  日期: $(cat $rtc/date 2>/dev/null || echo '无法读取')"
        fi
    done
else
    echo "未找到RTC系统信息"
fi
echo ""

# 9. 建议的解决方案
echo "9. 建议的解决方案:"
echo "   a) 如果CAN进程占用了RTC设备，可以发送SIGUSR1信号:"
if [ ! -z "$CAN_PIDS" ]; then
    for pid in $CAN_PIDS; do
        echo "      kill -USR1 $pid"
    done
fi
echo "   b) 强制终止占用RTC的进程:"
RTC_PIDS=$(lsof /dev/rtc* 2>/dev/null | grep -v COMMAND | awk '{print $2}' | sort -u)
if [ ! -z "$RTC_PIDS" ]; then
    for pid in $RTC_PIDS; do
        echo "      kill -TERM $pid  # 或 kill -KILL $pid"
    done
fi
echo "   c) 重新编译CAN程序，确保没有意外的RTC依赖"
echo ""

echo "=== 调试完成 ==="
