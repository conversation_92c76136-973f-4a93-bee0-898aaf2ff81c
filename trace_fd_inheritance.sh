#!/bin/bash

# 跟踪文件描述符继承和RTC设备访问的关联关系

echo "=== 文件描述符继承和RTC访问跟踪 ==="
echo "时间: $(date)"
echo ""

# 创建跟踪目录
TRACE_DIR="fd_trace_$(date +%Y%m%d_%H%M%S)"
mkdir -p "$TRACE_DIR"

echo "跟踪目录: $TRACE_DIR"
echo ""

# 函数：分析进程的文件描述符
analyze_process_fds() {
    local pid=$1
    local process_name=$2
    local output_file="$TRACE_DIR/fd_analysis_${pid}.txt"
    
    echo "=== 进程 $pid ($process_name) 文件描述符分析 ===" > "$output_file"
    echo "时间: $(date)" >> "$output_file"
    echo "" >> "$output_file"
    
    if [ -d "/proc/$pid" ]; then
        echo "进程信息:" >> "$output_file"
        ps -p $pid -o pid,ppid,cmd >> "$output_file" 2>/dev/null
        echo "" >> "$output_file"
        
        echo "父进程信息:" >> "$output_file"
        local ppid=$(ps -p $pid -o ppid= | tr -d ' ')
        if [ ! -z "$ppid" ] && [ "$ppid" != "0" ]; then
            ps -p $ppid -o pid,ppid,cmd >> "$output_file" 2>/dev/null
        fi
        echo "" >> "$output_file"
        
        echo "文件描述符列表:" >> "$output_file"
        if [ -d "/proc/$pid/fd" ]; then
            ls -la "/proc/$pid/fd/" >> "$output_file" 2>/dev/null
            echo "" >> "$output_file"
            
            echo "文件描述符详细映射:" >> "$output_file"
            for fd in /proc/$pid/fd/*; do
                if [ -L "$fd" ]; then
                    local fd_num=$(basename "$fd")
                    local target=$(readlink "$fd" 2>/dev/null)
                    printf "FD %2s -> %s\n" "$fd_num" "$target" >> "$output_file"
                fi
            done
            echo "" >> "$output_file"
            
            echo "特殊文件描述符分析:" >> "$output_file"
            echo "Socket文件描述符:" >> "$output_file"
            ls -la "/proc/$pid/fd/" 2>/dev/null | grep socket >> "$output_file" || echo "  无socket" >> "$output_file"
            echo "" >> "$output_file"
            
            echo "RTC相关文件描述符:" >> "$output_file"
            ls -la "/proc/$pid/fd/" 2>/dev/null | grep rtc >> "$output_file" || echo "  无RTC设备" >> "$output_file"
            echo "" >> "$output_file"
            
            echo "日志文件描述符:" >> "$output_file"
            ls -la "/proc/$pid/fd/" 2>/dev/null | grep -E "(log|temp)" >> "$output_file" || echo "  无日志文件" >> "$output_file"
            echo "" >> "$output_file"
        fi
        
        echo "进程环境变量:" >> "$output_file"
        cat "/proc/$pid/environ" 2>/dev/null | tr '\0' '\n' >> "$output_file"
        echo "" >> "$output_file"
        
        echo "进程状态:" >> "$output_file"
        cat "/proc/$pid/status" 2>/dev/null >> "$output_file"
        echo "" >> "$output_file"
        
    else
        echo "进程 $pid 不存在或无法访问" >> "$output_file"
    fi
}

# 函数：跟踪socket关联关系
trace_socket_relationships() {
    local output_file="$TRACE_DIR/socket_analysis.txt"
    
    echo "=== Socket关联关系分析 ===" > "$output_file"
    echo "时间: $(date)" >> "$output_file"
    echo "" >> "$output_file"
    
    echo "所有进程的socket使用情况:" >> "$output_file"
    lsof -i >> "$output_file" 2>/dev/null
    echo "" >> "$output_file"
    
    echo "Unix domain socket:" >> "$output_file"
    lsof -U >> "$output_file" 2>/dev/null
    echo "" >> "$output_file"
    
    echo "特定socket [42802] 的使用情况:" >> "$output_file"
    lsof | grep "42802" >> "$output_file" 2>/dev/null || echo "未找到socket [42802]" >> "$output_file"
    echo "" >> "$output_file"
    
    echo "网络连接状态:" >> "$output_file"
    netstat -an >> "$output_file" 2>/dev/null
    echo "" >> "$output_file"
}

# 主分析流程
echo "1. 获取当前所有CAN进程..."
CAN_PIDS=$(ps aux | grep can_type | grep -v grep | awk '{print $2}')

if [ -z "$CAN_PIDS" ]; then
    echo "未找到CAN进程，启动测试环境..."
    # 这里可以添加启动CAN进程的代码
    echo "请手动启动CAN测试，然后重新运行此脚本"
    exit 1
fi

echo "找到的CAN进程:"
ps aux | grep can_type | grep -v grep | nl
echo ""

echo "2. 分析每个CAN进程的文件描述符..."
for pid in $CAN_PIDS; do
    echo "分析进程 $pid..."
    analyze_process_fds "$pid" "can_type"
done

echo ""
echo "3. 分析test_start进程..."
TEST_START_PID=$(ps aux | grep test_start | grep -v grep | awk '{print $2}' | head -1)
if [ ! -z "$TEST_START_PID" ]; then
    echo "分析test_start进程 $TEST_START_PID..."
    analyze_process_fds "$TEST_START_PID" "test_start"
fi

echo ""
echo "4. 分析socket关联关系..."
trace_socket_relationships

echo ""
echo "5. 生成RTC访问关联分析..."
RTC_ANALYSIS_FILE="$TRACE_DIR/rtc_correlation_analysis.txt"

cat > "$RTC_ANALYSIS_FILE" << EOF
RTC设备访问关联分析报告
生成时间: $(date)

=== 关键发现 ===
1. 文件描述符分配异常：
   - 正常CAN进程：17个文件描述符
   - 异常CAN进程：19个文件描述符
   - 异常进程的FD16指向/dev/rtc0

2. Socket共享模式：
   - socket:[42802] 被多个进程共享
   - 这可能是进程间通信的socket

3. 文件描述符继承：
   - 异常进程可能从父进程继承了额外的文件描述符
   - 导致后续文件分配错位

=== 分析结论 ===
$(echo "当前占用RTC的进程:")
$(lsof /dev/rtc* 2>/dev/null || echo "无进程占用RTC")

=== 建议的解决方案 ===
1. 检查进程启动顺序和父子关系
2. 分析socket [42802] 的用途和创建时机
3. 确认文件描述符继承机制
4. 在CAN进程启动时显式关闭不需要的文件描述符

=== 详细分析文件 ===
EOF

# 列出所有生成的分析文件
for file in "$TRACE_DIR"/*.txt; do
    if [ -f "$file" ]; then
        echo "- $(basename "$file")" >> "$RTC_ANALYSIS_FILE"
    fi
done

echo ""
echo "6. 实时监控文件描述符变化..."
MONITOR_FILE="$TRACE_DIR/fd_monitor.log"
echo "开始监控文件描述符变化（按Ctrl+C停止）..." > "$MONITOR_FILE"
echo "时间: $(date)" >> "$MONITOR_FILE"
echo "" >> "$MONITOR_FILE"

# 监控循环（运行30秒）
for i in {1..30}; do
    echo "=== 监控周期 $i ===" >> "$MONITOR_FILE"
    echo "时间: $(date)" >> "$MONITOR_FILE"
    
    echo "RTC设备占用:" >> "$MONITOR_FILE"
    lsof /dev/rtc* 2>/dev/null >> "$MONITOR_FILE" || echo "无占用" >> "$MONITOR_FILE"
    
    echo "CAN进程数量:" >> "$MONITOR_FILE"
    ps aux | grep can_type | grep -v grep | wc -l >> "$MONITOR_FILE"
    
    echo "" >> "$MONITOR_FILE"
    sleep 1
done

echo ""
echo "7. 生成最终报告..."
FINAL_REPORT="$TRACE_DIR/FINAL_ANALYSIS_REPORT.txt"

cat > "$FINAL_REPORT" << EOF
文件描述符继承和RTC访问最终分析报告
========================================

生成时间: $(date)
跟踪目录: $TRACE_DIR

=== 执行摘要 ===
本次分析针对为什么只有第5个CAN进程（PID 9614）占用了RTC设备的问题。

关键发现：
1. 第5个CAN进程有19个文件描述符，而其他进程只有17个
2. 多出的文件描述符包括socket:[42802]和/dev/rtc0
3. socket:[42802]被多个进程共享，可能是IPC机制

=== 技术分析 ===
问题根源可能是：
1. 文件描述符继承：第5个进程从父进程继承了额外的FD
2. 时序竞争：在特定时刻，系统资源分配发生了变化
3. Socket共享：进程间通信导致的副作用

=== 建议措施 ===
1. 在CAN进程启动时显式关闭不需要的文件描述符
2. 检查父进程（test_start）的文件描述符管理
3. 分析socket:[42802]的创建和使用目的
4. 实施我们之前开发的RTC管理工具

=== 相关文件 ===
$(ls -la "$TRACE_DIR"/*.txt 2>/dev/null | awk '{print $9}' | xargs -I {} basename {})

EOF

echo "分析完成！"
echo ""
echo "生成的文件："
echo "- 最终报告: $FINAL_REPORT"
echo "- 详细分析: $TRACE_DIR/"
echo ""
echo "要查看最终报告："
echo "  cat $FINAL_REPORT"
echo ""
echo "要查看特定进程的详细分析："
echo "  ls $TRACE_DIR/fd_analysis_*.txt"
