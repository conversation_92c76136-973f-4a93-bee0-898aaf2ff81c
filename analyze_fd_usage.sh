#!/bin/bash

# 分析CAN进程文件描述符使用情况的脚本

echo "=== CAN进程文件描述符分析 ==="
echo "时间: $(date)"
echo ""

# 查找所有CAN进程
CAN_PIDS=$(ps aux | grep can_type | grep -v grep | awk '{print $2}')

if [ -z "$CAN_PIDS" ]; then
    echo "没有找到can_type进程"
    exit 1
fi

echo "找到的CAN进程:"
ps aux | grep can_type | grep -v grep
echo ""

for pid in $CAN_PIDS; do
    echo "=== 进程 $pid 的文件描述符分析 ==="
    
    # 检查进程是否还存在
    if [ ! -d "/proc/$pid" ]; then
        echo "进程 $pid 已不存在"
        continue
    fi
    
    echo "进程信息:"
    ps -p $pid -o pid,ppid,cmd
    echo ""
    
    echo "文件描述符列表:"
    if [ -d "/proc/$pid/fd" ]; then
        ls -la /proc/$pid/fd/ | nl
        echo ""
        
        echo "文件描述符详细信息:"
        for fd in /proc/$pid/fd/*; do
            if [ -L "$fd" ]; then
                fd_num=$(basename "$fd")
                target=$(readlink "$fd" 2>/dev/null)
                printf "FD %2s -> %s\n" "$fd_num" "$target"
                
                # 特别标记RTC设备
                if echo "$target" | grep -q "rtc"; then
                    echo "    *** 发现RTC设备! ***"
                fi
            fi
        done
        echo ""
        
        echo "RTC相关文件描述符:"
        ls -la /proc/$pid/fd/ | grep rtc || echo "    无RTC相关文件描述符"
        echo ""
        
        echo "socket文件描述符:"
        ls -la /proc/$pid/fd/ | grep socket || echo "    无socket文件描述符"
        echo ""
        
        echo "日志文件描述符:"
        ls -la /proc/$pid/fd/ | grep -E "(log|temp)" || echo "    无日志文件描述符"
        echo ""
        
    else
        echo "无法访问 /proc/$pid/fd/"
    fi
    
    echo "进程打开的文件 (lsof):"
    lsof -p $pid 2>/dev/null | head -20
    echo ""
    
    echo "进程环境变量 (与时间相关):"
    cat /proc/$pid/environ 2>/dev/null | tr '\0' '\n' | grep -i -E "(time|rtc|clock)" || echo "    无时间相关环境变量"
    echo ""
    
    echo "进程内存映射 (与RTC相关):"
    cat /proc/$pid/maps 2>/dev/null | grep -i rtc || echo "    无RTC相关内存映射"
    echo ""
    
    echo "----------------------------------------"
done

echo ""
echo "=== 系统RTC设备状态 ==="
echo "RTC设备列表:"
ls -la /dev/rtc* 2>/dev/null || echo "未找到RTC设备"
echo ""

echo "当前占用RTC设备的所有进程:"
lsof /dev/rtc* 2>/dev/null || echo "没有进程占用RTC设备"
echo ""

echo "=== 可能的原因分析 ==="
echo "1. 文件描述符编号巧合:"
echo "   - 不同进程的文件描述符编号可能相同"
echo "   - 只有一个进程实际打开了RTC设备"
echo ""

echo "2. 进程启动顺序:"
echo "   - 第一个启动的CAN进程可能触发了某种系统初始化"
echo "   - 后续进程没有重复这个操作"
echo ""

echo "3. 共享库初始化:"
echo "   - 某个共享库只在第一次加载时访问RTC"
echo "   - 后续进程使用已初始化的库"
echo ""

echo "4. 系统服务交互:"
echo "   - 某个系统服务可能与第一个CAN进程有特殊交互"
echo "   - 导致RTC设备被意外打开"
echo ""

echo "=== 建议的调试步骤 ==="
echo "1. 使用strace跟踪CAN进程启动:"
echo "   strace -e trace=openat,open,close -f ./can_type can0 2>&1 | grep rtc"
echo ""
echo "2. 检查进程启动顺序和时间:"
echo "   ps -eo pid,lstart,cmd | grep can_type"
echo ""
echo "3. 监控文件描述符变化:"
echo "   watch -n 1 'lsof /dev/rtc* 2>/dev/null'"
echo ""

echo "=== 分析完成 ==="
