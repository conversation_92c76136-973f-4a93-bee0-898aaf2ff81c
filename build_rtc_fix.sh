#!/bin/bash

# 构建RTC问题修复工具集

echo "=== RTC问题修复工具构建脚本 ==="
echo "时间: $(date)"
echo ""

# 设置编译器
CC=${CC:-gcc}
CFLAGS="-Wall -Wextra -std=c99 -O2"

# 创建构建目录
BUILD_DIR="build_rtc_fix"
mkdir -p "$BUILD_DIR"

echo "1. 编译RTC管理工具..."
if [ -f "rtc_manager.c" ]; then
    $CC $CFLAGS -o "$BUILD_DIR/rtc_manager" rtc_manager.c
    if [ $? -eq 0 ]; then
        echo "   ✓ rtc_manager 编译成功"
    else
        echo "   ✗ rtc_manager 编译失败"
        exit 1
    fi
else
    echo "   ✗ 找不到 rtc_manager.c"
    exit 1
fi

echo ""
echo "2. 编译文件描述符清理工具..."
if [ -f "fix_fd_inheritance.c" ]; then
    $CC $CFLAGS -o "$BUILD_DIR/fix_fd_inheritance" fix_fd_inheritance.c
    if [ $? -eq 0 ]; then
        echo "   ✓ fix_fd_inheritance 编译成功"
    else
        echo "   ✗ fix_fd_inheritance 编译失败"
        exit 1
    fi
else
    echo "   ✗ 找不到 fix_fd_inheritance.c"
    exit 1
fi

echo ""
echo "3. 编译更新后的CAN程序..."
if [ -d "can/can_performance" ]; then
    cd can/can_performance
    make clean
    make
    if [ $? -eq 0 ]; then
        echo "   ✓ CAN程序编译成功"
        cp can_type "../../$BUILD_DIR/"
    else
        echo "   ✗ CAN程序编译失败"
        cd ../..
        exit 1
    fi
    cd ../..
else
    echo "   ✗ 找不到 can/can_performance 目录"
    exit 1
fi

echo ""
echo "4. 编译更新后的测试管理程序..."
if [ -d "stress_test" ]; then
    cd stress_test
    if [ -f "Makefile" ]; then
        make clean
        make
        if [ $? -eq 0 ]; then
            echo "   ✓ 测试管理程序编译成功"
            cp test_start "../$BUILD_DIR/" 2>/dev/null || echo "   注意: test_start 可能不存在"
        else
            echo "   ✗ 测试管理程序编译失败"
        fi
    else
        echo "   注意: stress_test 目录没有 Makefile"
    fi
    cd ..
else
    echo "   ✗ 找不到 stress_test 目录"
fi

echo ""
echo "5. 复制脚本文件..."
cp debug_rtc_usage.sh "$BUILD_DIR/" 2>/dev/null
cp trace_can_startup.sh "$BUILD_DIR/" 2>/dev/null
cp analyze_fd_usage.sh "$BUILD_DIR/" 2>/dev/null
cp trace_fd_inheritance.sh "$BUILD_DIR/" 2>/dev/null
chmod +x "$BUILD_DIR"/*.sh 2>/dev/null

echo ""
echo "6. 创建安装脚本..."
cat > "$BUILD_DIR/install.sh" << 'EOF'
#!/bin/bash

# RTC修复工具安装脚本

echo "=== RTC修复工具安装 ==="

# 检查权限
if [ "$EUID" -ne 0 ]; then
    echo "注意: 建议以root权限运行以安装到系统目录"
    INSTALL_DIR="$HOME/bin"
    mkdir -p "$INSTALL_DIR"
else
    INSTALL_DIR="/usr/local/bin"
fi

echo "安装目录: $INSTALL_DIR"

# 安装二进制文件
for tool in rtc_manager fix_fd_inheritance; do
    if [ -f "$tool" ]; then
        cp "$tool" "$INSTALL_DIR/"
        chmod +x "$INSTALL_DIR/$tool"
        echo "已安装: $tool"
    fi
done

# 安装脚本
for script in *.sh; do
    if [ -f "$script" ] && [ "$script" != "install.sh" ]; then
        cp "$script" "$INSTALL_DIR/"
        chmod +x "$INSTALL_DIR/$script"
        echo "已安装: $script"
    fi
done

echo ""
echo "安装完成!"
echo "工具位置: $INSTALL_DIR"
echo ""
echo "使用方法:"
echo "  $INSTALL_DIR/rtc_manager -h"
echo "  $INSTALL_DIR/fix_fd_inheritance -h"
echo "  $INSTALL_DIR/debug_rtc_usage.sh"
EOF

chmod +x "$BUILD_DIR/install.sh"

echo ""
echo "7. 创建使用说明..."
cat > "$BUILD_DIR/README.txt" << 'EOF'
RTC问题修复工具集
================

本工具集用于解决CAN测试程序意外占用RTC设备的问题。

工具列表:
---------
1. rtc_manager          - RTC设备管理工具
2. fix_fd_inheritance   - 文件描述符清理工具
3. can_type            - 更新后的CAN测试程序
4. debug_rtc_usage.sh  - RTC使用情况调试脚本
5. trace_*.sh          - 各种跟踪分析脚本

快速使用:
---------
1. 检查RTC设备占用:
   ./rtc_manager -c -v

2. 清理RTC设备占用:
   ./rtc_manager -f -v

3. 调试RTC占用问题:
   ./debug_rtc_usage.sh

4. 清理进程的文件描述符:
   ./fix_fd_inheritance -s -v

安装:
-----
运行 ./install.sh 将工具安装到系统路径

问题排查:
---------
如果问题仍然存在:
1. 运行 debug_rtc_usage.sh 收集信息
2. 运行 trace_fd_inheritance.sh 分析文件描述符
3. 检查系统日志: dmesg | grep -i rtc

更多信息请参考 RTC_ISSUE_SOLUTION.md
EOF

echo ""
echo "8. 测试工具..."
echo "测试 rtc_manager:"
"$BUILD_DIR/rtc_manager" -h > /dev/null 2>&1
if [ $? -eq 0 ]; then
    echo "   ✓ rtc_manager 工作正常"
else
    echo "   ✗ rtc_manager 测试失败"
fi

echo "测试 fix_fd_inheritance:"
"$BUILD_DIR/fix_fd_inheritance" -h > /dev/null 2>&1
if [ $? -eq 0 ]; then
    echo "   ✓ fix_fd_inheritance 工作正常"
else
    echo "   ✗ fix_fd_inheritance 测试失败"
fi

echo ""
echo "=== 构建完成 ==="
echo ""
echo "生成的文件:"
ls -la "$BUILD_DIR/"
echo ""
echo "下一步:"
echo "1. 进入构建目录: cd $BUILD_DIR"
echo "2. 安装工具: ./install.sh"
echo "3. 测试修复: ./rtc_manager -c -v"
echo "4. 运行调试: ./debug_rtc_usage.sh"
echo ""
echo "如果需要重新部署CAN程序，请将 $BUILD_DIR/can_type 复制到目标位置"
