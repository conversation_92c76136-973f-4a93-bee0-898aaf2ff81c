#!/bin/bash

# 跟踪CAN进程启动过程中的RTC访问

echo "=== CAN进程启动跟踪脚本 ==="
echo "用于分析为什么只有一个CAN进程占用RTC设备"
echo ""

# 检查是否有strace
if ! command -v strace >/dev/null 2>&1; then
    echo "错误: 需要strace工具来进行跟踪"
    echo "请安装strace: apt-get install strace 或 yum install strace"
    exit 1
fi

# 检查CAN程序是否存在
CAN_PROGRAM="./can_type"
if [ ! -f "$CAN_PROGRAM" ]; then
    echo "错误: 找不到CAN程序 $CAN_PROGRAM"
    echo "请确保在正确的目录下运行此脚本"
    exit 1
fi

# 创建跟踪日志目录
TRACE_DIR="trace_logs"
mkdir -p "$TRACE_DIR"

echo "1. 清理现有的CAN进程..."
pkill -f can_type 2>/dev/null
sleep 2

echo "2. 检查RTC设备初始状态..."
echo "RTC设备占用情况:"
lsof /dev/rtc* 2>/dev/null || echo "  无进程占用RTC设备"
echo ""

echo "3. 启动第一个CAN进程并跟踪..."
TRACE_FILE1="$TRACE_DIR/can_trace_1.log"
echo "跟踪文件: $TRACE_FILE1"

# 启动第一个CAN进程，跟踪所有文件操作
strace -e trace=openat,open,close,mkdir,write,read -f -o "$TRACE_FILE1" \
    $CAN_PROGRAM can2 -t 8 1000 &
CAN_PID1=$!

echo "第一个CAN进程PID: $CAN_PID1"
sleep 3

echo "4. 检查第一个进程启动后的RTC状态..."
lsof /dev/rtc* 2>/dev/null || echo "  无进程占用RTC设备"
echo ""

echo "5. 启动第二个CAN进程并跟踪..."
TRACE_FILE2="$TRACE_DIR/can_trace_2.log"
echo "跟踪文件: $TRACE_FILE2"

strace -e trace=openat,open,close,mkdir,write,read -f -o "$TRACE_FILE2" \
    $CAN_PROGRAM can3 -t 8 1000 &
CAN_PID2=$!

echo "第二个CAN进程PID: $CAN_PID2"
sleep 3

echo "6. 检查两个进程启动后的RTC状态..."
lsof /dev/rtc* 2>/dev/null || echo "  无进程占用RTC设备"
echo ""

echo "7. 启动第三个CAN进程并跟踪..."
TRACE_FILE3="$TRACE_DIR/can_trace_3.log"
echo "跟踪文件: $TRACE_FILE3"

strace -e trace=openat,open,close,mkdir,write,read -f -o "$TRACE_FILE3" \
    $CAN_PROGRAM can4 -t 8 1000 &
CAN_PID3=$!

echo "第三个CAN进程PID: $CAN_PID3"
sleep 3

echo "8. 检查所有进程启动后的RTC状态..."
echo "当前CAN进程:"
ps aux | grep can_type | grep -v grep
echo ""
echo "RTC设备占用情况:"
lsof /dev/rtc* 2>/dev/null || echo "  无进程占用RTC设备"
echo ""

echo "9. 分析跟踪结果..."
echo ""
echo "=== 第一个进程的RTC相关操作 ==="
if [ -f "$TRACE_FILE1" ]; then
    grep -i rtc "$TRACE_FILE1" || echo "未发现直接的RTC访问"
    echo ""
    echo "文件描述符16的相关操作:"
    grep "= 16" "$TRACE_FILE1" || echo "未发现文件描述符16的操作"
fi
echo ""

echo "=== 第二个进程的RTC相关操作 ==="
if [ -f "$TRACE_FILE2" ]; then
    grep -i rtc "$TRACE_FILE2" || echo "未发现直接的RTC访问"
    echo ""
    echo "文件描述符16的相关操作:"
    grep "= 16" "$TRACE_FILE2" || echo "未发现文件描述符16的操作"
fi
echo ""

echo "=== 第三个进程的RTC相关操作 ==="
if [ -f "$TRACE_FILE3" ]; then
    grep -i rtc "$TRACE_FILE3" || echo "未发现直接的RTC访问"
    echo ""
    echo "文件描述符16的相关操作:"
    grep "= 16" "$TRACE_FILE3" || echo "未发现文件描述符16的操作"
fi
echo ""

echo "10. 详细的文件描述符分析..."
for pid in $CAN_PID1 $CAN_PID2 $CAN_PID3; do
    if [ -d "/proc/$pid" ]; then
        echo "进程 $pid 的文件描述符16:"
        if [ -L "/proc/$pid/fd/16" ]; then
            ls -la "/proc/$pid/fd/16"
        else
            echo "  文件描述符16不存在"
        fi
    fi
done
echo ""

echo "11. 清理进程..."
kill $CAN_PID1 $CAN_PID2 $CAN_PID3 2>/dev/null
sleep 2
pkill -f can_type 2>/dev/null

echo "12. 生成分析报告..."
REPORT_FILE="$TRACE_DIR/analysis_report.txt"
cat > "$REPORT_FILE" << EOF
CAN进程RTC占用分析报告
生成时间: $(date)

进程信息:
- 第一个进程PID: $CAN_PID1
- 第二个进程PID: $CAN_PID2  
- 第三个进程PID: $CAN_PID3

跟踪文件:
- $TRACE_FILE1
- $TRACE_FILE2
- $TRACE_FILE3

分析结论:
$(if grep -q "rtc" "$TRACE_FILE1" 2>/dev/null; then
    echo "第一个进程有RTC相关操作"
else
    echo "第一个进程无直接RTC操作"
fi)

$(if grep -q "rtc" "$TRACE_FILE2" 2>/dev/null; then
    echo "第二个进程有RTC相关操作"
else
    echo "第二个进程无直接RTC操作"
fi)

$(if grep -q "rtc" "$TRACE_FILE3" 2>/dev/null; then
    echo "第三个进程有RTC相关操作"
else
    echo "第三个进程无直接RTC操作"
fi)

建议:
1. 检查跟踪文件中的文件描述符分配模式
2. 分析是否存在文件描述符泄漏
3. 确认RTC设备访问的确切原因
EOF

echo "分析完成!"
echo "详细报告保存在: $REPORT_FILE"
echo "跟踪文件保存在: $TRACE_DIR/"
echo ""
echo "要查看详细的系统调用，请检查跟踪文件:"
echo "  less $TRACE_FILE1"
echo "  less $TRACE_FILE2" 
echo "  less $TRACE_FILE3"
