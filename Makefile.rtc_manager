# Makefile for RTC Manager Tool
# 用于编译RTC设备管理工具

# 编译器设置
CC ?= gcc
CFLAGS = -Wall -Wextra -std=c99 -O2
TARGET = rtc_manager
SOURCE = rtc_manager.c

# 默认目标
all: $(TARGET)

# 编译RTC管理工具
$(TARGET): $(SOURCE)
	$(CC) $(CFLAGS) -o $(TARGET) $(SOURCE)
	@echo "RTC Manager tool compiled successfully"

# 安装到系统路径（可选）
install: $(TARGET)
	@if [ -w /usr/local/bin ]; then \
		cp $(TARGET) /usr/local/bin/; \
		chmod +x /usr/local/bin/$(TARGET); \
		echo "Installed $(TARGET) to /usr/local/bin/"; \
	else \
		echo "Cannot install to /usr/local/bin (permission denied)"; \
		echo "You can manually copy $(TARGET) to your preferred location"; \
	fi

# 清理编译文件
clean:
	rm -f $(TARGET)
	@echo "Cleaned up build files"

# 测试工具
test: $(TARGET)
	@echo "Testing RTC Manager tool..."
	./$(TARGET) -h
	@echo "Running check mode..."
	./$(TARGET) -c -v

# 显示帮助
help:
	@echo "Available targets:"
	@echo "  all      - Build the RTC manager tool"
	@echo "  install  - Install to /usr/local/bin"
	@echo "  clean    - Remove build files"
	@echo "  test     - Test the tool"
	@echo "  help     - Show this help"

.PHONY: all install clean test help
