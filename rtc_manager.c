#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <unistd.h>
#include <signal.h>
#include <errno.h>
#include <sys/types.h>
#include <sys/wait.h>

/*
 * RTC设备管理工具
 * 用于在测试前清理占用RTC设备的进程
 */

void print_usage(const char *prog_name) {
    printf("Usage: %s [options]\n", prog_name);
    printf("Options:\n");
    printf("  -d <device>    RTC device path (default: /dev/rtc0)\n");
    printf("  -f             Force kill processes using RTC\n");
    printf("  -c             Check only, don't kill processes\n");
    printf("  -v             Verbose output\n");
    printf("  -h             Show this help\n");
}

int check_rtc_usage(const char *rtc_device, int verbose) {
    char cmd[256];
    FILE *fp;
    char line[512];
    int found_processes = 0;
    
    snprintf(cmd, sizeof(cmd), "lsof %s 2>/dev/null", rtc_device);
    
    if (verbose) {
        printf("Checking processes using %s...\n", rtc_device);
    }
    
    fp = popen(cmd, "r");
    if (fp == NULL) {
        fprintf(stderr, "Failed to execute lsof command\n");
        return -1;
    }
    
    // 跳过标题行
    if (fgets(line, sizeof(line), fp) != NULL) {
        if (verbose) {
            printf("COMMAND     PID USER   FD   TYPE DEVICE SIZE/OFF NODE NAME\n");
        }
    }
    
    while (fgets(line, sizeof(line), fp) != NULL) {
        found_processes++;
        printf("%s", line);
        
        if (verbose) {
            char process_name[64];
            int pid;
            if (sscanf(line, "%s %d", process_name, &pid) == 2) {
                printf("  -> Process: %s, PID: %d\n", process_name, pid);
            }
        }
    }
    
    pclose(fp);
    return found_processes;
}

int kill_rtc_processes(const char *rtc_device, int force, int verbose) {
    char cmd[256];
    FILE *fp;
    char line[512];
    int killed_count = 0;
    
    snprintf(cmd, sizeof(cmd), "lsof %s 2>/dev/null | grep -v COMMAND", rtc_device);
    
    fp = popen(cmd, "r");
    if (fp == NULL) {
        fprintf(stderr, "Failed to execute lsof command\n");
        return -1;
    }
    
    while (fgets(line, sizeof(line), fp) != NULL) {
        char process_name[64];
        int pid;
        
        if (sscanf(line, "%s %d", process_name, &pid) == 2 && pid > 0) {
            if (verbose) {
                printf("Found process %s (PID: %d) using %s\n", process_name, pid, rtc_device);
            }
            
            // 特殊处理can_type进程
            if (strstr(process_name, "can_type") != NULL) {
                if (verbose) {
                    printf("Sending SIGUSR1 to can_type process %d...\n", pid);
                }
                
                if (kill(pid, SIGUSR1) == 0) {
                    sleep(2); // 等待进程响应
                    
                    // 检查是否还在使用RTC
                    char check_cmd[256];
                    snprintf(check_cmd, sizeof(check_cmd), "lsof %s 2>/dev/null | grep %d", rtc_device, pid);
                    FILE *check_fp = popen(check_cmd, "r");
                    char check_line[256];
                    int still_using = 0;
                    
                    if (check_fp != NULL) {
                        if (fgets(check_line, sizeof(check_line), check_fp) != NULL) {
                            still_using = 1;
                        }
                        pclose(check_fp);
                    }
                    
                    if (!still_using) {
                        if (verbose) {
                            printf("Process %d successfully released RTC device\n", pid);
                        }
                        killed_count++;
                        continue;
                    } else if (verbose) {
                        printf("Process %d still using RTC after SIGUSR1\n", pid);
                    }
                }
            }
            
            // 如果SIGUSR1不起作用或不是can_type进程，使用SIGTERM
            if (verbose) {
                printf("Sending SIGTERM to process %d...\n", pid);
            }
            
            if (kill(pid, SIGTERM) == 0) {
                sleep(1);
                
                // 检查进程是否还存在
                if (kill(pid, 0) == 0) {
                    if (force) {
                        if (verbose) {
                            printf("Process %d still exists, sending SIGKILL...\n", pid);
                        }
                        kill(pid, SIGKILL);
                        sleep(1);
                    } else {
                        printf("Process %d still exists after SIGTERM (use -f to force kill)\n", pid);
                    }
                }
                killed_count++;
            } else {
                fprintf(stderr, "Failed to send signal to process %d: %s\n", pid, strerror(errno));
            }
        }
    }
    
    pclose(fp);
    return killed_count;
}

int main(int argc, char *argv[]) {
    const char *rtc_device = "/dev/rtc0";
    int force = 0;
    int check_only = 0;
    int verbose = 0;
    int opt;
    
    while ((opt = getopt(argc, argv, "d:fcvh")) != -1) {
        switch (opt) {
            case 'd':
                rtc_device = optarg;
                break;
            case 'f':
                force = 1;
                break;
            case 'c':
                check_only = 1;
                break;
            case 'v':
                verbose = 1;
                break;
            case 'h':
                print_usage(argv[0]);
                return 0;
            default:
                print_usage(argv[0]);
                return 1;
        }
    }
    
    printf("RTC Device Manager\n");
    printf("Target device: %s\n", rtc_device);
    printf("Mode: %s\n", check_only ? "Check only" : "Clean up");
    printf("\n");
    
    int process_count = check_rtc_usage(rtc_device, verbose);
    
    if (process_count < 0) {
        return 1;
    }
    
    if (process_count == 0) {
        printf("No processes found using %s\n", rtc_device);
        return 0;
    }
    
    printf("Found %d process(es) using %s\n", process_count, rtc_device);
    
    if (!check_only) {
        printf("\nAttempting to clean up processes...\n");
        int killed = kill_rtc_processes(rtc_device, force, verbose);
        
        if (killed >= 0) {
            printf("Processed %d process(es)\n", killed);
            
            // 再次检查
            printf("\nRechecking device usage...\n");
            int remaining = check_rtc_usage(rtc_device, 0);
            if (remaining == 0) {
                printf("RTC device %s is now free\n", rtc_device);
                return 0;
            } else {
                printf("Warning: %d process(es) still using %s\n", remaining, rtc_device);
                return 1;
            }
        } else {
            fprintf(stderr, "Failed to clean up processes\n");
            return 1;
        }
    }
    
    return 0;
}
