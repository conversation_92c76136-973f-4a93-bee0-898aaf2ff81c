#include <stdio.h>
#include <stdlib.h>
#include <unistd.h>
#include <fcntl.h>
#include <dirent.h>
#include <string.h>
#include <sys/stat.h>

/*
 * 文件描述符清理工具
 * 用于在CAN进程启动时清理不需要的文件描述符
 * 特别是防止意外继承RTC设备的文件描述符
 */

void print_usage(const char *prog_name) {
    printf("Usage: %s [options]\n", prog_name);
    printf("Options:\n");
    printf("  -p <pid>       Clean file descriptors for specific process\n");
    printf("  -s             Clean current process (self)\n");
    printf("  -d             Dry run - only show what would be cleaned\n");
    printf("  -v             Verbose output\n");
    printf("  -h             Show this help\n");
    printf("\n");
    printf("This tool helps prevent RTC device inheritance issues in CAN processes.\n");
}

int is_essential_fd(int fd) {
    // 保留标准输入输出和错误
    if (fd <= 2) return 1;
    
    // 检查文件描述符指向的内容
    char fd_path[64];
    char target[256];
    snprintf(fd_path, sizeof(fd_path), "/proc/self/fd/%d", fd);
    
    ssize_t len = readlink(fd_path, target, sizeof(target) - 1);
    if (len > 0) {
        target[len] = '\0';
        
        // 保留socket（可能是必要的IPC）
        if (strstr(target, "socket:") != NULL) return 1;
        
        // 保留pipe（可能是必要的IPC）
        if (strstr(target, "pipe:") != NULL) return 1;
        
        // 保留日志文件（CAN程序需要）
        if (strstr(target, ".log") != NULL) return 1;
        
        // 保留CAN设备
        if (strstr(target, "/dev/can") != NULL) return 1;
        
        // 保留tty设备
        if (strstr(target, "/dev/tty") != NULL) return 1;
        
        // 不保留RTC设备！
        if (strstr(target, "/dev/rtc") != NULL) return 0;
        
        // 不保留其他设备文件
        if (strncmp(target, "/dev/", 5) == 0) return 0;
    }
    
    return 1; // 默认保留
}

int clean_process_fds(pid_t pid, int dry_run, int verbose) {
    char fd_dir[64];
    DIR *dir;
    struct dirent *entry;
    int cleaned_count = 0;
    
    if (pid == 0) {
        pid = getpid();
    }
    
    snprintf(fd_dir, sizeof(fd_dir), "/proc/%d/fd", pid);
    
    if (verbose) {
        printf("Checking file descriptors for process %d...\n", pid);
    }
    
    dir = opendir(fd_dir);
    if (dir == NULL) {
        perror("opendir");
        return -1;
    }
    
    while ((entry = readdir(dir)) != NULL) {
        if (entry->d_name[0] == '.') continue;
        
        int fd = atoi(entry->d_name);
        if (fd <= 0) continue;
        
        char fd_path[64];
        char target[256];
        snprintf(fd_path, sizeof(fd_path), "/proc/%d/fd/%d", pid, fd);
        
        ssize_t len = readlink(fd_path, target, sizeof(target) - 1);
        if (len > 0) {
            target[len] = '\0';
            
            if (verbose) {
                printf("FD %d -> %s", fd, target);
            }
            
            if (!is_essential_fd(fd)) {
                if (verbose) {
                    printf(" [WILL CLOSE]");
                }
                
                if (!dry_run) {
                    if (pid == getpid()) {
                        // 关闭当前进程的文件描述符
                        if (close(fd) == 0) {
                            cleaned_count++;
                            if (verbose) {
                                printf(" [CLOSED]");
                            }
                        } else {
                            if (verbose) {
                                printf(" [CLOSE FAILED]");
                            }
                        }
                    } else {
                        // 对于其他进程，我们无法直接关闭FD
                        // 只能建议终止进程
                        if (verbose) {
                            printf(" [CANNOT CLOSE - DIFFERENT PROCESS]");
                        }
                    }
                } else {
                    cleaned_count++;
                }
            }
            
            if (verbose) {
                printf("\n");
            }
        }
    }
    
    closedir(dir);
    return cleaned_count;
}

int main(int argc, char *argv[]) {
    pid_t target_pid = 0;
    int dry_run = 0;
    int verbose = 0;
    int self_clean = 0;
    int opt;
    
    while ((opt = getopt(argc, argv, "p:sdvh")) != -1) {
        switch (opt) {
            case 'p':
                target_pid = atoi(optarg);
                break;
            case 's':
                self_clean = 1;
                break;
            case 'd':
                dry_run = 1;
                break;
            case 'v':
                verbose = 1;
                break;
            case 'h':
                print_usage(argv[0]);
                return 0;
            default:
                print_usage(argv[0]);
                return 1;
        }
    }
    
    if (target_pid == 0 && !self_clean) {
        printf("Error: Must specify either -p <pid> or -s\n");
        print_usage(argv[0]);
        return 1;
    }
    
    if (self_clean) {
        target_pid = getpid();
    }
    
    printf("File Descriptor Cleaner\n");
    printf("Target process: %d\n", target_pid);
    printf("Mode: %s\n", dry_run ? "Dry run" : "Clean");
    printf("\n");
    
    int result = clean_process_fds(target_pid, dry_run, verbose);
    
    if (result >= 0) {
        printf("Processed %d file descriptors\n", result);
        
        if (!dry_run && result > 0) {
            printf("Successfully cleaned %d file descriptors\n", result);
        }
        
        // 验证RTC设备是否还被占用
        printf("\nChecking RTC device usage after cleanup...\n");
        char check_cmd[256];
        snprintf(check_cmd, sizeof(check_cmd), "lsof /dev/rtc* 2>/dev/null | grep %d", target_pid);
        
        FILE *fp = popen(check_cmd, "r");
        if (fp != NULL) {
            char line[256];
            int found_rtc = 0;
            while (fgets(line, sizeof(line), fp) != NULL) {
                printf("Warning: Process still has RTC open: %s", line);
                found_rtc = 1;
            }
            pclose(fp);
            
            if (!found_rtc) {
                printf("Good: Process no longer has RTC device open\n");
            }
        }
        
        return 0;
    } else {
        printf("Error: Failed to process file descriptors\n");
        return 1;
    }
}
