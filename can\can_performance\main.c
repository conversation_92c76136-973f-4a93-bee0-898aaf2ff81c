#include <errno.h>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <unistd.h>
#include <fcntl.h>
#include <termios.h> /*PPSIX 终端控制定义*/
#include <net/if.h>
#include <sys/ioctl.h>
#include <sys/socket.h>
#include <linux/can.h>
#include <linux/can/raw.h>
#include <sys/time.h>
#include <sys/stat.h>
#include <time.h>
#include <signal.h>
#include <dirent.h>  /* 添加目录操作支持 */
#include "mycan.h"
#include "display.h"
#include "file.h"

char ver[20] = {"ver03.001"};

int sock_fd;
struct_can_param can_param = {1000000, 0x103, {
                                                  0x100,
                                                  0x00,
                                              },
                              0,
                              0,
                              0,
                              4000000,
                              0.75,
                              0.75};

// 数据收发应用
unsigned char send_buff[2200], receive_buff[2200], temp_rx_buff[2200];
unsigned int send_num = 0, receive_num = 0;

int active_send_mode = 0, active_send_num = 8, active_send_time = 2000, receive_timeout = 0, temp_rx_point = 0;
int real_send_num = 0;
int loopback_send_mode = 0;
unsigned long last_time = 0, present_time = 0;
unsigned long total_send = 0, total_receive = 0;
unsigned long total_send_group = 0, want_send_times = 0;
char count_numb = 0;
int err_count = 0, receive_state = 0;

// 存储日志
char history_log_path[10] = {"log"};
char temp_log_path[10] = {"temp"};
char history_log_name[50] = {0};
char temp_log_name[50] = {0};
char log_path[50] = {"/root/"};
FILE *pHistoryLogFile = NULL;
FILE *pTempLogFile = NULL;

char dev[20];

/*
 * @description : 自定义打印函数
 * @param - buff:  打印数据缓冲区
 * @param - lens:  打印数据长度
 * @param - mode:  打印格式
 * @return		: 无
 */
void func_my_print(unsigned char *buff, unsigned int lens, unsigned char mode, unsigned char save)
{
    int i = 0;

    switch (mode)
    {
    case 'H': // 按照16进制打印数据
        for (i = 0; i < lens; i++)
        {
            if (save)
                file_write_fmt(pHistoryLogFile, "0X%02X  ", buff[i]);
            else
                printf("0X%02X  ", buff[i]);
        }
        break;
    case 'h': // 按照16进制打印数据
        for (i = 0; i < lens; i++)
        {
            if (save)
                file_write_fmt(pHistoryLogFile, "%02x  ", buff[i]);
            else
                printf("%02x  ", buff[i]);
        }
        break;
    case 'd': // 按照10进制打印数据
        for (i = 0; i < lens; i++)
        {
            if (save)
                file_write_fmt(pHistoryLogFile, "%02d  ", buff[i]);
            else
                printf("%02d  ", buff[i]);
        }
        break;
    case 'c': // 按照字符打印数据
        for (i = 0; i < lens; i++)
        {
            if (save)
                file_write_fmt(pHistoryLogFile, "%c", buff[i]);
            else
                printf("%c", buff[i]);
        }
        break;
    default:
        break;
    }
    if (!save)
        printf("\n");
    else
        file_write_fmt(pHistoryLogFile, "\n\n");
}
unsigned long func_get_system_time_ms(void)
{
    struct timespec time = {0, 0};

    clock_gettime(CLOCK_MONOTONIC, &time);
    return (time.tv_sec * 1000 + time.tv_nsec / 1000000);
}

/*
 * @description  : 打印参数设置格式
 * @param - pname: 函数名
 * @return		 : 无
 */
static void print_usage(const char *pname)
{
    printf("Usage: %s device [-b baudrate] [-s sample] [-id id_numb] [-filt id mask] [-e] [-fd dbaudrate] [-ds dsample] [-t len time] [-c times] [-l] [-L] [-p path] [-v]"
           "\n\t'-b baudrate' for different baudrate, range 5k to 5M,unit is Kbps"
           "\n\t'-s sample' for different sample point, range 0 to 99,"
           "\n\t'-id id_numb' for nonzero can send id"
           "\n\t'-filt id mask' for receive can_id and can_mask"
           "\n\t'-e' for extend id frame"
           "\n\t'-fd dbaudrate' for can fd mode , dbaudrate range 250k to 8M,unit is Kbps"
           "\n\t'-ds dsample' for different data sample point, range 0 to 99,"
           "\n\t'-t len time'  for interval set time actively sends the len data, unit is ms,time should not be less than 1"
           "\n\t'-c times'  for total send times"
           "\n\t'-l' for can loopback mode"
           "\n\t'-L' for app loopback receive data"
           "\n\t'-p path' for log file path  example : /root/"
           "\n\t'-v' show version"
           "\n\texample : can0 baudrate 250k extend id 40 Bytes was send every 2s--> ./can_demo can0 -b 250 -e -t 40 2000"
           "\n\texample : can1 baudrate 250k extend id forward the received data back to it --> ./can_demo can1 -b 250 -e -L"
           "\n\texample : can0 canfd mode baudrate 250k data rate 4M extend id sample point 0.8 64 Bytes was send every 2s--> ./can_demo can0 -b 250 -s 80 -e -t 64 2000 -fd 4000 -ds 80"
           "\n\texample : can0 canfd mode baudrate 250k extend id dbaudrate 4000k forward the received data back to it --> ./can_demo can0 -b 250 -s 80 -fd 4000 -ds 80 -e -L\n ",
           pname);
}

/*
 * @description : 解析函数带入参数
 * @param - numb: 参数个数
 * @param - *param: 带入参数数组指针
 * @param - *canparam: can应用参数
 * @return		: 无
 */
void get_param(int numb, char *param[], struct_can_param *canparam)
{
    int i = 0, len = 0, j = 0;
    unsigned int baudrate = 0, id = 0, mask = 0;

    if (numb <= 2)
        return;

    for (i = 2; i < numb; i++)
    {
        if (!strcmp(param[i], "-b"))
        {
            i++;
            baudrate = atoi(param[i]);
            switch (baudrate)
            {
            case 5:
            case 10:
            case 20:
            case 40:
            case 50:
            case 80:
            case 100:
            case 125:
            case 200:
            case 250:
            case 400:
            case 500:
            case 666:
            case 800:
            case 1000:
            case 2000:
            case 4000:
            case 5000:
                canparam->baudrate = baudrate * 1000;
                break;
            }
            continue;
        }
        if (!strcmp(param[i], "-s"))
        {
            i++;
            len = atoi(param[i]);
            if ((len > 0) && (len < 99))
                canparam->sample_point = (float)len / 100;
        }
        if (!strcmp(param[i], "-id"))
        {
            i++;
            id = strtoul(param[i], NULL, 16);
            if (id)
            {
                canparam->id = (unsigned int)id;
            }
            continue;
        }
        if (!strcmp(param[i], "-filt"))
        {
            i++;
            id = 0;
            id = strtoul(param[i], NULL, 16);
            canparam->filter.can_id = (canid_t)id;
            i++;
            mask = strtoul(param[i], NULL, 16);
            canparam->filter.can_mask = (canid_t)mask;
            continue;
        }
        if (!strcmp(param[i], "-e"))
        {
            canparam->extend = 1;
            continue;
        }
        if (!strcmp(param[i], "-fd"))
        {
            canparam->canfd_mode = CAN_FD_MODE;

            i++;
            baudrate = atoi(param[i]);
            switch (baudrate)
            {
            case 250:
            case 500:
            case 1000:
            case 2000:
            case 3000:
            case 4000:
            case 5000:
            case 8000:
                canparam->data_baudrate = baudrate * 1000;
                break;
            }
            continue;
        }
        if (!strcmp(param[i], "-ds"))
        {
            i++;
            len = atoi(param[i]);
            if ((len > 0) && (len < 99))
                canparam->data_sample_point = (float)len / 100;
        }
        if (!strcmp(param[i], "-t"))
        {
            active_send_mode = 1;

            i++;
            len = atoi(param[i]);
            // len = strlen(param[i]);
            if (len > 0)
            {
                active_send_num = len;
                for (j = 0; j < active_send_num; j++)
                {
                    send_buff[j] = j;
                    if (!send_buff[j])
                        send_buff[j] = 1;
                }

                i++;
                len = atoi(param[i]); // 发送间隔
                if (len > 1)
                {
                    // active_send_time = len * 1000; //转换为ms单位
                    active_send_time = len; // 转换为ms单位
                    receive_timeout = (int)(active_send_time * 0.8);
                }
                else
                {
                    printf("The sending interval cannot be less than 1.\n");
                    print_usage(param[0]);
                    exit(1);
                }
            }
            continue;
        }
        if (!strcmp(param[i], "-c"))
        {
            i++;
            len = atoi(param[i]);
            if (len > 0)
            {
                want_send_times = len;
            }
            continue;
        }
        if (!strcmp(param[i], "-l"))
        {
            canparam->loopback_mode = 1;
            continue;
        }
        if (!strcmp(param[i], "-L"))
        {
            loopback_send_mode = 1;
            continue;
        }
        if (!strcmp(param[i], "-p"))
        {
            i++;
            strcpy(log_path, param[i]);
            continue;
        }
        if (!strcmp(param[i], "-v"))
        {
            printf("can_demo ver:  %s\n", ver);
            continue;
        }
    }
}
/*
 * @description    : 信号处理函数
 * @param - iSignNo: 信号
 * @return		   : 执行结果
 */
void SignHandler(int iSignNo)
{
    int t = 50;

    while (t > 1)
    {
        if (can_param.canfd_mode == CAN_FD_MODE)
        {
            receive_num = func_receive_canfd_buff(sock_fd, receive_buff, sizeof(receive_buff));
        }
        else
        {
            receive_num = func_receive_can_buff(sock_fd, receive_buff, sizeof(receive_buff));
        }
        usleep(20);
        t--;
    }

    // 检查是否意外打开了RTC设备
    char check_cmd[200] = {0};
    sprintf(check_cmd, "lsof /dev/rtc* 2>/dev/null | grep %d", getpid());
    FILE *check_fp = popen(check_cmd, "r");
    if (check_fp != NULL)
    {
        char line[256] = {0};
        if (fgets(line, sizeof(line), check_fp) != NULL)
        {
            printf("[WARNING] : CAN process %d has RTC device open: %s", getpid(), line);
            printf("[WARNING] : This may cause RTC test failures\n");
        }
        pclose(check_fp);
    }

    printf("total_send=%ld, total_receive=%ld \n", total_send, total_receive);
    file_write_time_fmt(pTempLogFile, "%s total_send=%ld, total_receive=%ld \n", dev, total_send, total_receive);
    if (err_count)
    {
        file_write_time_fmt(pTempLogFile, "%s test result is FAILED \n", dev);
        printf(L_RED "%s test result is FAILED" NONE "\n", dev);
    }
}

/*
 * @description    : RTC设备释放信号处理函数
 * @param - iSignNo: 信号
 * @return		   : 执行结果
 */
void RTCReleaseHandler(int iSignNo)
{
    printf("[INFO] : Received SIGUSR1, checking for RTC device usage...\n");

    // 检查当前进程是否打开了RTC设备
    char check_cmd[200] = {0};
    sprintf(check_cmd, "lsof /dev/rtc* 2>/dev/null | grep %d", getpid());
    FILE *check_fp = popen(check_cmd, "r");
    if (check_fp != NULL)
    {
        char line[256] = {0};
        while (fgets(line, sizeof(line), check_fp) != NULL)
        {
            printf("[INFO] : Found RTC usage: %s", line);
            // 这里可以添加具体的RTC文件描述符关闭逻辑
            // 但由于CAN程序理论上不应该打开RTC，这主要用于调试
        }
        pclose(check_fp);
    }

    printf("[INFO] : RTC release check completed\n");
    else
    {
        file_write_time_fmt(pTempLogFile, "%s test result is PASSED \n", dev);
        printf(L_GREEN "%s test result is PASSED" NONE "\n", dev);
    }

    fflush(pHistoryLogFile);     /*将数据同步至ROM*/
    file_close(pHistoryLogFile); /*关闭文件*/

    fflush(pTempLogFile);     /*将数据同步至ROM*/
    file_close(pTempLogFile); /*关闭文件*/

    usleep(20000);
    close(sock_fd);
    exit(1);
}

/*
 * @description  : 主函数
 * @param - argc : 参数个数
 * @param - *argv: 带入参数数组指针
 * @return		 : 执行结果
 */
int main(int argc, char *argv[])
{
    int result = 0;
    char history_log_full_name[100] = {0};
    char temp_log_full_name[100] = {0};

    // 检测是否有参数
    if (argc < 2)
    {
        print_usage(argv[0]);
        exit(1);
    }

    // 检测是否有--h或--help
    if ((!strcmp(argv[1], "--h")) || (!strcmp(argv[1], "--help")))
    {
        print_usage(argv[0]);
        exit(1);
    }

    strcpy(dev, argv[1]);

    // 在程序启动时检查并清理可能继承的RTC文件描述符
    printf("[INFO] : Checking for inherited RTC file descriptors...\n");
    char check_cmd[200];
    sprintf(check_cmd, "lsof /dev/rtc* 2>/dev/null | grep %d", getpid());
    FILE *check_fp = popen(check_cmd, "r");
    if (check_fp != NULL) {
        char line[256];
        while (fgets(line, sizeof(line), check_fp) != NULL) {
            printf("[WARNING] : Found inherited RTC descriptor: %s", line);

            // 尝试找到并关闭RTC文件描述符
            char fd_dir[64];
            sprintf(fd_dir, "/proc/%d/fd", getpid());
            DIR *dir = opendir(fd_dir);
            if (dir != NULL) {
                struct dirent *entry;
                while ((entry = readdir(dir)) != NULL) {
                    if (entry->d_name[0] == '.') continue;

                    int fd = atoi(entry->d_name);
                    if (fd <= 2) continue; // 保留标准输入输出错误

                    char fd_path[64];
                    char target[256];
                    sprintf(fd_path, "/proc/%d/fd/%d", getpid(), fd);

                    ssize_t len = readlink(fd_path, target, sizeof(target) - 1);
                    if (len > 0) {
                        target[len] = '\0';
                        if (strstr(target, "/dev/rtc") != NULL) {
                            printf("[INFO] : Closing inherited RTC FD %d -> %s\n", fd, target);
                            close(fd);
                        }
                    }
                }
                closedir(dir);
            }
        }
        pclose(check_fp);
    }

    // 从main函数带来的参数解析为CAN口参数
    get_param(argc, argv, &can_param);

    // 准备历史日志文件
    file_add_full_fath(history_log_full_name, log_path, history_log_path);
    if ((access(history_log_full_name, 0)) == -1)
    {
        printf("not exist\n");
        mkdir(history_log_full_name, S_IRWXU);
    }
    sprintf(history_log_name, "/%s_history.log", dev);
    strcat(history_log_full_name, history_log_name);
    pHistoryLogFile = file_open(history_log_full_name, ATWR);
    if (NULL == pHistoryLogFile)
        exit(1);
    // 准备临时日志文件
    file_add_full_fath(temp_log_full_name, log_path, temp_log_path);
    if ((access(temp_log_full_name, 0)) == -1)
    {
        printf("not exist\n");
        mkdir(temp_log_full_name, S_IRWXU);
    }
    sprintf(temp_log_name, "/%s_temp.log", dev);
    strcat(temp_log_full_name, temp_log_name);
    pTempLogFile = file_open(temp_log_full_name, WTWR);
    if (NULL == pTempLogFile)
        exit(1);

    // 给历史文件和临时文件附测试头
    file_write_head(pHistoryLogFile, can_param, dev);
    file_write_head(pTempLogFile, can_param, dev);
    file_write_time_fmt(pHistoryLogFile, "[%s nwrite=%d]  ", dev, active_send_num);
    func_my_print(send_buff, active_send_num, 'h', 1); // 将收到的数据打印出来

    // 进程被kill 需要保存文件
    signal(SIGTERM, SignHandler);
    // 按下 ctrl + c 需要保存文件
    signal(SIGINT, SignHandler);
    // RTC设备释放信号处理
    signal(SIGUSR1, RTCReleaseHandler);

    // 当知道设备名称时可以直接赋值dev，例strcpy(dev, "can0");
    // 打开CAN口 创建socket 绑定socket
    if (can_param.canfd_mode == CAN_FD_MODE)
    {
        sock_fd = func_open_canfd(dev, can_param);
    }
    else
    {
        sock_fd = func_open_can(dev, can_param);
    }
    if (sock_fd < 0)
    {
        printf("Can't Open deveice %s \n", dev);
        exit(0);
    }
    else
    {
        printf("baudrate = %ldK,can_id = 0x%x,can_filter_id = 0x%x,can_filter_mask = 0x%x,extend id = %d,loopback mode = %d,canfd mode = %d,dbaudrate = %ldK\n",
               can_param.baudrate / 1000, can_param.id, can_param.filter.can_id, can_param.filter.can_mask,
               can_param.extend, can_param.loopback_mode, can_param.canfd_mode, can_param.data_baudrate / 1000);
        // 设置CAN口过滤
        if (can_param.canfd_mode == CAN_FD_MODE)
        {
            result = func_set_canfd(sock_fd, can_param);
        }
        else
        {
            result = func_set_can(sock_fd, can_param);
        }
        if (result < 0)
        {
            perror("set_opt error");
            exit(0);
        }
        // 设置CAN口为非阻塞方式
        fcntl(sock_fd, F_SETFL, O_NONBLOCK); // 非阻塞
    }

    while (1)
    {
        // 接收数据
        if (can_param.canfd_mode == CAN_FD_MODE)
        {
            receive_num = func_receive_canfd_buff(sock_fd, receive_buff, sizeof(receive_buff));
        }
        else
        {
            receive_num = func_receive_can_buff(sock_fd, receive_buff, sizeof(receive_buff));
        }
        /*if (receive_num > 0)
        {
            file_write_time_fmt(pHistoryLogFile, "[%s nread=%d]  ", dev, receive_num);
            func_my_print(receive_buff, receive_num, 'h', 1); // 将收到的数据打印出来
        }*/
        // 组织发送数据
        if ((1 == loopback_send_mode) && (receive_num > 0)) // 数据回环处理
        {
            file_write_time_fmt(pHistoryLogFile, "[%s nread=%d]  ", dev, receive_num);
            func_my_print(receive_buff, receive_num, 'h', 1); // 将收到的数据打印出来
            send_num = receive_num;
            memcpy(send_buff, receive_buff, receive_num);
        }
        else if (1 == active_send_mode)
        {
            present_time = func_get_system_time_ms();

            if (WAIT_RECEIVE == receive_state)
            {
                if (receive_num > 0)
                {
                    memcpy(&temp_rx_buff[temp_rx_point], receive_buff, receive_num);
                    temp_rx_point += receive_num;
                    if (temp_rx_point >= active_send_num)
                    {
                        // printf("real_use=%ld,time_out=%d\n", (present_time - last_time), receive_timeout);
                        if (0 == strncmp((const char *)temp_rx_buff, (const char *)send_buff, active_send_num))
                        {
                            receive_state = OK_RECEIVE;
                        }
                        else
                        {
                            printf(L_RED "%s data receive and send diffrent.!!!" NONE "\n", dev);
                            file_write_time_fmt(pHistoryLogFile, "%s data receive and send diffrent.!!!\n", dev);
                            // len = Func_Dprintf(data, "%s test times %d,spi data receive and send diffrent.\n", dev, run->times);
                            // fwrite(data, sizeof(char), len, pfile); /*写入数据*/
                            file_write_time_fmt(pHistoryLogFile, "[%s nread=%d]  ", dev, temp_rx_point);
                            func_my_print(temp_rx_buff, temp_rx_point, 'h', 1); // 将收到的数据打印出来
                            err_count++;
                            receive_state = ERR_RECEIVE;
                        }
                    }
                }
                if ((present_time - last_time) >= receive_timeout)
                {
                    printf(L_RED "%s data receive time out.!!!" NONE "\n", dev);
                    file_write_time_fmt(pHistoryLogFile, "%s data receive time out.!!!\n", dev);
                    // len = Func_Dprintf(data, "%s test times %d,spi data receive and send diffrent.\n", dev, run->times);
                    // fwrite(data, sizeof(char), len, pfile); /*写入数据*/
                    if (temp_rx_point > 0)
                    {
                        file_write_time_fmt(pHistoryLogFile, "[%s nread=%d]  ", dev, temp_rx_point);
                        func_my_print(temp_rx_buff, temp_rx_point, 'h', 1); // 将收到的数据打印出来
                    }
                    err_count++;
                    receive_state = ERR_RECEIVE;
                }
            }

            if ((present_time - last_time) >= active_send_time)
            {
                send_num = active_send_num;

                last_time = func_get_system_time_ms();
                receive_state = WAIT_RECEIVE;
                temp_rx_point = 0;
            }
        }

        // 发送数据
        if (send_num > 0)
        {
            if (want_send_times)
            {
                if (total_send_group >= want_send_times)
                {
                    printf("total_send=%ld, total_receive=%ld \n", total_send, total_receive);
                    file_write_time_fmt(pTempLogFile, "%s total_send=%ld, total_receive=%ld \n", dev, total_send, total_receive);
                    if (err_count)
                    {
                        file_write_time_fmt(pTempLogFile, "%s test result is FAILED \n", dev);
                        printf(L_RED "%s test result is FAILED" NONE "\n", dev);
                    }
                    else
                    {
                        file_write_time_fmt(pTempLogFile, "%s test result is PASSED \n", dev);
                        printf(L_GREEN "%s test result is PASSED" NONE "\n", dev);
                    }
                    fflush(pHistoryLogFile);     /*将数据同步至ROM*/
                    file_close(pHistoryLogFile); /*关闭文件*/

                    fflush(pTempLogFile);     /*将数据同步至ROM*/
                    file_close(pTempLogFile); /*关闭文件*/

                    close(sock_fd);
                    exit(0);
                }
            }

            if (can_param.canfd_mode == CAN_FD_MODE)
            {
                real_send_num = func_send_canfd_buff(sock_fd, send_buff, send_num, can_param);
            }
            else
            {
                real_send_num = func_send_can_buff(sock_fd, send_buff, send_num, can_param);
            }
            if (real_send_num > 0)
            {

                total_send_group++;
                // file_write_time_fmt(pHistoryLogFile, "[%s nwrite=%d]  ", dev, real_send_num);
                // func_my_print(send_buff, real_send_num, 'h', 1); // 将收到的数据打印出来
            }
            if (0 == active_send_mode)
                memset(send_buff, 0, send_num);
            send_num = 0;
        }
        usleep(50000); // 50ms
    }
    close(sock_fd);
    exit(0);
}
